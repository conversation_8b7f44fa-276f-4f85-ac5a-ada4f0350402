<?php
require_once $_SERVER['DOCUMENT_ROOT'].'/config-ggportal.php';
require_once $include_path . 'header-include.php'; //functions and class
require_once $class_path . 'class.phpmailer.php';
require_once $class_path . 'class.smtp.php';
// $_SESSION['redirect']="index.php";


//$address = '<EMAIL>';
//$subject = 'testing';

function send_mail($subject,$address,$body) {
    // Uncomment as needed for debugging
    //error_reporting(E_ALL);
    //error_reporting(E_STRICT);
    // Set as needed
    //date_default_timezone_set('America/New_York');
    $mail = new PHPMailer(); 
    // Optionally get email body from external file
    //$body = file_get_contents("http://localhost/promotion-mail-contents.php");
    //    echo $body;
    //    die();
    //$body = eregi_replace("[\]",'',$body);
    $mail->IsSMTP();                            // telling the class to use SMTP
    $mail->Host       = "mail.edvios.io";       // SMTP server
    $mail->SMTPDebug  = 1;                      // enables SMTP debug information (for testing)
    // 0 default no debugging messages
    // 1 = errors and messages
    // 2 = messages only
    $mail->SMTPAuth   = true;                   // enable SMTP authentication
    $mail->SMTPSecure = 'ssl';                // Not supported
    //$mail->SMTPSecure = 'tls';                  // Supported
    $mail->Host       = "mail.edvios.io";       // sets the SMTP server
    $mail->Port       = 465;                    // set the SMTP port for the GMAIL server
    $mail->Username   = "<EMAIL>";         // SMTP account username (how you login at gmail)
    $mail->Password   = "8A#83^0aOkvQ";      // SMTP account password (how you login at gmail)

    $mail->setFrom('<EMAIL>', 'Edvios');

    //    $mail->addReplyTo('<EMAIL>', 'Nb Couriers');
 
    $mail->Subject    = $subject;
 
    $mail->AltBody    = "To view the message, please use an HTML compatible email viewer!"; // optional, comment out and test
 
    $mail->msgHTML($body);
 
    //$address = "<EMAIL>";
    $mail->addAddress($address, "");
    // if you have attachments
    //$mail->addAttachment("phpmailer.gif");      // attachment 
    //$mail->addAttachment("phpmailer_mini.gif"); // attachment
 
    if(!$mail->Send()) {
      echo "Mailer Error: " . $mail->ErrorInfo;
      die();
      $_SESSION['error']="There was a problem while Sending mails! Please try again.";
    } else {
      //echo "Message sent!";
      $error_type="M";
      return 1;
      //errorRedirect("Successfully saved. Promotion mail are sent", "promotion-customer-add.php",$error_type);
    }
}

// Test the connection
//send_mail($subject,$address);


?>
