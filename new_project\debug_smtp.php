<?php
// SMTP Debug Test File
echo "<h2>🔍 SMTP Configuration Debug</h2>";

require_once $_SERVER['DOCUMENT_ROOT'].'/config-ggportal.php';
require_once $include_path . 'header-include.php';
require_once $class_path . 'class.phpmailer.php';
require_once $class_path . 'class.smtp.php';

echo "<h3>Current Configuration in form-submit-email.php:</h3>";

// Test the current configuration
$mail = new PHPMailer();
$mail->IsSMTP();
$mail->Host       = "sandbox.smtp.mailtrap.io";
$mail->SMTPDebug  = 2;
$mail->SMTPAuth   = true;
$mail->SMTPSecure = 'tls';
$mail->Port = 2525;
$mail->Username = 'fbf439dfc0fbb4';
$mail->Password = 'f936b59342a758';

echo "<strong>Current Settings:</strong><br>";
echo "Host: " . $mail->Host . "<br>";
echo "Port: " . $mail->Port . "<br>";
echo "Security: " . $mail->SMTPSecure . "<br>";
echo "Username: " . $mail->Username . "<br>";
echo "Password: " . $mail->Password . "<br>";
echo "Auth Enabled: " . ($mail->SMTPAuth ? 'Yes' : 'No') . "<br>";

echo "<hr>";

echo "<h3>🚨 Issues Found:</h3>";
echo "<ol>";
echo "<li><strong>Port Mismatch:</strong> You're using TLS with port 2525. For TLS, use port 587.</li>";
echo "<li><strong>Wrong setFrom:</strong> You're using 'smtp.mailtrap.io' as sender email instead of a proper email.</li>";
echo "<li><strong>Credential Verification Needed:</strong> Let's test if these credentials are correct.</li>";
echo "</ol>";

echo "<hr>";

echo "<h3>✅ Recommended Fixes:</h3>";

echo "<h4>Option 1: TLS Configuration (Recommended)</h4>";
echo "<pre>";
echo "\$mail->Host       = \"sandbox.smtp.mailtrap.io\";\n";
echo "\$mail->SMTPAuth   = true;\n";
echo "\$mail->SMTPSecure = 'tls';\n";
echo "\$mail->Port       = 587;  // Change from 2525 to 587\n";
echo "\$mail->Username   = 'fbf439dfc0fbb4';\n";
echo "\$mail->Password   = 'f936b59342a758';\n";
echo "\$mail->setFrom('<EMAIL>', 'Edvios');  // Use proper email\n";
echo "</pre>";

echo "<h4>Option 2: No Encryption (Alternative)</h4>";
echo "<pre>";
echo "\$mail->Host       = \"sandbox.smtp.mailtrap.io\";\n";
echo "\$mail->SMTPAuth   = true;\n";
echo "\$mail->SMTPSecure = '';  // No encryption\n";
echo "\$mail->Port       = 2525;\n";
echo "\$mail->Username   = 'fbf439dfc0fbb4';\n";
echo "\$mail->Password   = 'f936b59342a758';\n";
echo "\$mail->setFrom('<EMAIL>', 'Edvios');\n";
echo "</pre>";

echo "<hr>";

echo "<h3>🧪 Testing Connection:</h3>";

// Test Option 1: TLS + Port 587
echo "<h4>Test 1: TLS + Port 587</h4>";
$mail1 = new PHPMailer();
$mail1->IsSMTP();
$mail1->Host       = "sandbox.smtp.mailtrap.io";
$mail1->SMTPDebug  = 2;
$mail1->SMTPAuth   = true;
$mail1->SMTPSecure = 'tls';
$mail1->Port       = 587;
$mail1->Username   = 'fbf439dfc0fbb4';
$mail1->Password   = 'f936b59342a758';
$mail1->setFrom('<EMAIL>', 'Edvios');
$mail1->addAddress('<EMAIL>', 'Test User');
$mail1->Subject = 'Test Email - TLS';
$mail1->Body = 'This is a test email using TLS configuration.';

echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0;'>";
echo "<strong>Attempting to send with TLS + Port 587...</strong><br>";
if(!$mail1->Send()) {
    echo "<span style='color: red;'>❌ Failed: " . $mail1->ErrorInfo . "</span>";
} else {
    echo "<span style='color: green;'>✅ Success: Email sent with TLS configuration!</span>";
}
echo "</div>";

echo "<hr>";

// Test Option 2: No encryption + Port 2525
echo "<h4>Test 2: No Encryption + Port 2525</h4>";
$mail2 = new PHPMailer();
$mail2->IsSMTP();
$mail2->Host       = "sandbox.smtp.mailtrap.io";
$mail2->SMTPDebug  = 2;
$mail2->SMTPAuth   = true;
$mail2->SMTPSecure = '';
$mail2->Port       = 2525;
$mail2->Username   = 'fbf439dfc0fbb4';
$mail2->Password   = 'f936b59342a758';
$mail2->setFrom('<EMAIL>', 'Edvios');
$mail2->addAddress('<EMAIL>', 'Test User');
$mail2->Subject = 'Test Email - No Encryption';
$mail2->Body = 'This is a test email using no encryption configuration.';

echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0;'>";
echo "<strong>Attempting to send with No Encryption + Port 2525...</strong><br>";
if(!$mail2->Send()) {
    echo "<span style='color: red;'>❌ Failed: " . $mail2->ErrorInfo . "</span>";
} else {
    echo "<span style='color: green;'>✅ Success: Email sent with no encryption configuration!</span>";
}
echo "</div>";

echo "<hr>";

echo "<h3>📋 Next Steps:</h3>";
echo "<ol>";
echo "<li><strong>Check your Mailtrap inbox settings</strong> at <a href='https://mailtrap.io' target='_blank'>mailtrap.io</a></li>";
echo "<li><strong>Verify the credentials</strong> match exactly (case-sensitive)</li>";
echo "<li><strong>Try both configurations above</strong> to see which one works</li>";
echo "<li><strong>Update your form-submit-email.php</strong> with the working configuration</li>";
echo "</ol>";

echo "<h3>🔗 Mailtrap Settings to Check:</h3>";
echo "<ul>";
echo "<li>Go to your Mailtrap dashboard</li>";
echo "<li>Select your inbox</li>";
echo "<li>Click on 'SMTP Settings'</li>";
echo "<li>Copy the exact credentials shown there</li>";
echo "</ul>";

?>
