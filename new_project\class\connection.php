<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of connection
 *
 * <AUTHOR>
class connection {
        /** This private $sql_details is for the server sde processing */

    //
    //qn6F6jhZBrwVu3b
    private $sql_details = array(
        'user' => 'gept_user',
        'pass' => 'Admin@123',
        'db'   => 'ggportal_tbl_agent',
        'host' => '**************'
        );

    function getSqlDetails(){
        return $this->sql_details;
    }


    protected function makeConnection() {


         //  localhost
         $dbhost = "**************";
         $dbuser = "gept_user";
         $dbpass = "Admin@123";
         $dbname = "ggportal_tbl_agent";


        global $_conn;
        try {
            //$con = mysql_connect($dbhost, $dbuser, $dbpass);
            $_conn = mysqli_connect($dbhost, $dbuser, $dbpass, $dbname); //, $port);
            //iconv_set_encoding("internal_encoding", "UTF-8");
            //iconv_set_encoding("output_encoding", "ISO-8859-1");

            if (!$_conn) {
                //die('Could not connect: ' . mysql_error());
                die('Could not connect to the database! Please try refreshing!!');
            } else {
                //echo "<br/>connectd<br/>";
            }
        } catch (Exception $e) {
            //echo $e->getMessage();
            die('Unexpected problem while connecting the database! Please try refreshing');
        }
        mysqli_select_db($_conn, $dbname);

        mysqli_query($_conn, "SET NAMES 'utf8'");
        return $_conn;
    }
    private $host = "**************";
    private $user ="gept_user";
    private $pass="Admin@123";
    private $db="ggportal_tbl_agent";

    private $conn;

    function __construct()
    {
        $this->conn = $this->connectDB();
    }
    function connectDB(){
        $conn = mysqli_connect($this->host,$this->user,$this->pass,$this->db);
        mysqli_set_charset($conn,"utf8");
        return $conn;
    }
    function runQuery($query){
        $result = mysqli_query($this->conn, $query);
        while($row=mysqli_fetch_assoc($result)){
            $resultset[] = $row;
        }
        if(!empty($resultset))
            return $resultset;
    }

    function numRows($query){
        $result = mysqli_query($this->conn, $query);
        $rowcount= mysqli_num_rows($result);
        return $rowcount;
        
    }
    function runQuery1($query1){
        $result = mysqli_query($this->conn, $query1);
        while($row=mysqli_fetch_assoc($result)){
            $resultset[] = $row;
        }
        if(!empty($resultset))
            return $resultset;
    }

    function numRows1($query1){
        $result = mysqli_query($this->conn, $query1);
        $rowcount= mysqli_num_rows($result);
        return $rowcount;
        
    }
    function runQuery2($query2){
        $result = mysqli_query($this->conn, $query2);
        while($row=mysqli_fetch_assoc($result)){
            $resultset[] = $row;
        }
        if(!empty($resultset))
            return $resultset;
    }

    function numRows2($query2){
        $result = mysqli_query($this->conn, $query2);
        $rowcount= mysqli_num_rows($result);
        return $rowcount;
        
    }

}
