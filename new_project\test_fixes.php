<?php
// Test file to verify both fixes work

echo "<h2>Testing Array Keys Fix</h2>";

// Test the array keys that were causing warnings
$test_details = array(
    'emergency_contact_relation' => 'Test Relation',
    'nic' => 'Test NIC',
    'address' => 'Test Address',
    'province' => 'Test Province',
    'postal-code' => 'Test Postal Code'
);

echo "Testing array keys:<br>";
foreach ($test_details as $key => $value) {
    echo "Key: '$key' = '$value'<br>";
}

echo "<br><h2>Testing SMTP Configuration</h2>";

// Include the email function
require_once 'form-submit-email.php';

echo "SMTP Settings:<br>";
echo "Host: sandbox.smtp.mailtrap.io<br>";
echo "Port: 587<br>";
echo "Security: TLS<br>";
echo "Username: d2260f2731b5b4<br>";
echo "Password: b1b6a6919f65c3<br>";

echo "<br><strong>To test email sending, uncomment the line below:</strong><br>";
echo "// \$result = send_mail('Test Subject', '<EMAIL>', 'Test email body');<br>";

// Uncomment the next line to actually test email sending
// $result = send_mail('Test Subject', '<EMAIL>', 'Test email body');

echo "<br><h2>Fixes Applied:</h2>";
echo "1. ✅ Added missing array keys to student-signup.php<br>";
echo "2. ✅ Fixed 'postal_code' to 'postal-code' mismatch<br>";
echo "3. ✅ Updated SMTP to use TLS encryption and port 587<br>";
echo "4. ✅ Increased debug level to 2 for better error reporting<br>";

?>
