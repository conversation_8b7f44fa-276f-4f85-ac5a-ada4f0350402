<?php

include_once 'connection.php';

class Program extends connection
{
    public function __construct()
    {
        $cconn = new connection();
        $this->_conn = $cconn->makeConnection();
    }


    public function Program()
    {
        self::__construct();
    }

    private $_conn;


    public function getProgramByID($program_id)
    {
        $conn = $this->_conn;
        $program_id = intval($program_id);
        if ($program_id > 0) {
            $cond = "and s.program_id=$program_id ";
        } else {
            $cond = " and 1=2 ";
        }
        $query = "SELECT *
                    FROM ggportal_tbl_program s
                    left join ggportal_tbl_country c ON c.country_id=s.country_id
                    left join ggportal_tbl_course co ON co.course_id=s.course_id
                    left join ggportal_tbl_institute i ON i.institute_id=s.institute_id
                    left join ggportal_tbl_currency cu ON cu.currency_id=s.currency_id
                    WHERE 1=1  $cond;";
        //        echo $query;
        //        die();
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }


    public function getPrograms($criteria)
    {
        $conn = $this->_conn;
        $cond = "";

        if (isset($criteria['course_id']) && strlen($criteria['course_id']) > 0) {
            $course_id = intval($criteria['course_id']);
            $cond .= " and co.course_id  = $course_id ";
        }

        if (isset($criteria['country_id']) && strlen($criteria['country_id']) > 0) {
            $country_id = intval($criteria['country_id']);
            $cond .= " and c.country_id  = $country_id ";
        }

        if (isset($criteria['user_id']) && strlen($criteria['user_id']) > 0) {
            $user_id = intval($criteria['user_id']);
            $cond .= " and pw.user_id  = $user_id ";
        }

        if (isset($criteria['user_type']) && strlen($criteria['user_type']) > 0) {
            $user_type = mysqli_real_escape_string($conn, $criteria['user_type']);
            $cond .= " and pw.user = '$user_type' ";
        }


        if (isset($criteria['program_id_str']) && strlen($criteria['program_id_str']) > 0) {
            $program_id_string = mysqli_real_escape_string($conn, $criteria['program_id_str']);
            $cond .= " and s.program_id in ($program_id_string) ";
        }


        if (isset($criteria['institute_id']) && strlen($criteria['institute_id']) > 0) {
            $institute_id = intval($criteria['institute_id']);
            $cond .= " and p.institute_id  = $institute_id ";
        }

        // if (isset($criteria['all_data']) > 0) {
        //     $all_data = intval($criteria['all_data']);

        //     if($all_data == 0) {
        //         $cond .= " and 1=2 ";
        //     }
        // }


        // $query = "SELECT s.*,c.*,co.*,i.*,cu.*,pw.program_wishlist_id
        // from ggportal_tbl_program s
        // left join ggportal_tbl_country c ON c.country_id=s.country_id
        // left join ggportal_tbl_course co ON co.course_id=s.course_id
        // left join ggportal_tbl_institute i ON i.institute_id=s.institute_id
        // left join ggportal_tbl_currency cu ON cu.currency_id=s.currency_id
        // left join ggportal_tbl_program_wishlist pw ON pw.program_id=s.program_id
        //  where 1=1  $cond GROUP BY s.program_id order by s.program_id DESC";

        $query = "SELECT s.*, c.*, co.*, i.*, cu.*, pw.program_wishlist_id 
          FROM ggportal_tbl_program s
          LEFT JOIN ggportal_tbl_country c ON c.country_id = s.country_id
          LEFT JOIN ggportal_tbl_course co ON co.course_id = s.course_id
          LEFT JOIN ggportal_tbl_institute i ON i.institute_id = s.institute_id
          LEFT JOIN ggportal_tbl_currency cu ON cu.currency_id = s.currency_id
          LEFT JOIN ggportal_tbl_program_wishlist pw ON pw.program_id = s.program_id
          WHERE 1 = 1 $cond
          -- REMOVE GROUP BY entirely

          ORDER BY s.program_id DESC";

        // if ($all_data == 0) {
        //     $cond .= " and 1=2 ";
        // }

        $result = mysqli_query($conn, $query);
        return db_to_array($result);
    }


    public function saveProgram($_details)
{
    $conn = $this->_conn;
    $program_id = intval($_details['program_id']);

    $last_program_id = 0;

    // Retrieve the last program_id
    $sql = "SELECT program_id FROM ggportal_tbl_program ORDER BY program_id DESC LIMIT 1";
    $result = $conn->query($sql);

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $last_program_id = intval($row["program_id"]);
    }

    // Increment the last program_id by 1
    $new_program_id = $last_program_id + 1;

    if ($program_id == 0) {
        // Insert new program with the incremented program_id
        $query = "INSERT INTO ggportal_tbl_program SET 
            program_id = $new_program_id,
            program_name = '" . mysqli_real_escape_string($conn, $_details['program_name']) . "',   
            course_type = '" . mysqli_real_escape_string($conn, $_details['course_type']) . "',                      
            course_id = " . intval($_details['course_id']) . ",                  
            country_id = " . intval($_details['country_id']) . ",  
            city = '" . mysqli_real_escape_string($conn, $_details['city']) . "',                
            institute_id = " . intval($_details['institute_id']) . ", 
            deadline = '" . mysqli_real_escape_string($conn, $_details['deadline']) . "',                 
            currency_id = " . intval($_details['currency_id']) . ",                  
            commission = '" . mysqli_real_escape_string($conn, $_details['commission']) . "',                
            tution_fee = '" . mysqli_real_escape_string($conn, $_details['tution_fee']) . "',                
            application_fee = '" . mysqli_real_escape_string($conn, $_details['application_fee']) . "',                
            intake = '" . mysqli_real_escape_string($conn, $_details['intake']) . "',                
            duration = '" . mysqli_real_escape_string($conn, $_details['duration']) . "',                
            ets = '" . mysqli_real_escape_string($conn, $_details['ets']) . "',                
            requirements = '" . mysqli_real_escape_string($conn, $_details['requirements']) . "',
            english_requirements = '" . mysqli_real_escape_string($conn, $_details['english_requirements']) . "',                
            program_web_url = '" . mysqli_real_escape_string($conn, $_details['program_web_url']) . "',  
            intake2 ='" . mysqli_real_escape_string($conn, $_details['intake2']) . "', 
            deadline2 ='" . mysqli_real_escape_string($conn, $_details['deadline2']) . "', 
            intake3 ='" . mysqli_real_escape_string($conn, $_details['intake3']) . "', 
            deadline3 ='" . mysqli_real_escape_string($conn, $_details['deadline3']) . "', 
            intake4 ='" . mysqli_real_escape_string($conn, $_details['intake4']) . "', 
            deadline4 ='" . mysqli_real_escape_string($conn, $_details['deadline4']) . "';";

    } else {
        // Update existing program
        $query = "UPDATE ggportal_tbl_program SET     
            program_name = '" . mysqli_real_escape_string($conn, $_details['program_name']) . "',  
            course_type = '" . mysqli_real_escape_string($conn, $_details['course_type']) . "',                                
            course_id = " . intval($_details['course_id']) . ",                  
            country_id = " . intval($_details['country_id']) . ",  
            city = '" . mysqli_real_escape_string($conn, $_details['city']) . "',                
            institute_id = " . intval($_details['institute_id']) . ", 
            deadline = '" . mysqli_real_escape_string($conn, $_details['deadline']) . "',                 
            currency_id = " . intval($_details['currency_id']) . ",                  
            commission = '" . mysqli_real_escape_string($conn, $_details['commission']) . "',                
            tution_fee = '" . mysqli_real_escape_string($conn, $_details['tution_fee']) . "',                
            application_fee = '" . mysqli_real_escape_string($conn, $_details['application_fee']) . "',                
            intake = '" . mysqli_real_escape_string($conn, $_details['intake']) . "',                
            duration = '" . mysqli_real_escape_string($conn, $_details['duration']) . "',                
            ets = '" . mysqli_real_escape_string($conn, $_details['ets']) . "',                
            requirements = '" . mysqli_real_escape_string($conn, $_details['requirements']) . "',
            english_requirements = '" . mysqli_real_escape_string($conn, $_details['english_requirements']) . "',
            program_web_url = '" . mysqli_real_escape_string($conn, $_details['program_web_url']) . "',
            intake2 ='" . mysqli_real_escape_string($conn, $_details['intake2']) . "', 
            deadline2 ='" . mysqli_real_escape_string($conn, $_details['deadline2']) . "', 
            intake3 ='" . mysqli_real_escape_string($conn, $_details['intake3']) . "', 
            deadline3 ='" . mysqli_real_escape_string($conn, $_details['deadline3']) . "', 
            intake4 ='" . mysqli_real_escape_string($conn, $_details['intake4']) . "', 
            deadline4 ='" . mysqli_real_escape_string($conn, $_details['deadline4']) . "'
            WHERE program_id = $program_id;";

    }

    $res = mysqli_query($conn, $query);
    if ($res) {
        if ($program_id == 0) {
            // If a new program is inserted, return the new program_id
            return $new_program_id;
        } else {
            // If an existing program is updated, return the same program_id
            return $program_id;
        }
    } else {
        return mysqli_error($conn);
    }
}


    function insertPrograms($_details)
    {
        $conn = $this->_conn;
        $course_name = mysqli_real_escape_string($conn, $_details['course_name']);
    
        // Check if the course name already exists
        $check_query = "SELECT course_id FROM ggportal_tbl_course WHERE course_name = ?";
        $stmt = mysqli_prepare($conn, $check_query);
        mysqli_stmt_bind_param($stmt, "s", $course_name);
        mysqli_stmt_execute($stmt);
        $check_result = mysqli_stmt_get_result($stmt);
    
        if(mysqli_num_rows($check_result) === 0) {
            // If course doesn't exist, insert it into ggportal_tbl_course
            $course_query = "INSERT INTO ggportal_tbl_course (course_name) VALUES (?)";
            $stmt = mysqli_prepare($conn, $course_query);
            mysqli_stmt_bind_param($stmt, "s", $course_name);
            $course_res = mysqli_stmt_execute($stmt);
            if (!$course_res) {
                throw new Exception(mysqli_error($conn)); // Throw an exception if course insertion fails
            }
            $_details['course_id'] = mysqli_insert_id($conn); // Retrieve the newly inserted course ID
        } else {
            // If course already exists, retrieve its course_id
            $row = mysqli_fetch_assoc($check_result);
            $_details['course_id'] = $row['course_id'];
        }
        // $conn = $this->_conn;
        $query = "INSERT INTO ggportal_tbl_program SET
                program_id ='" . mysqli_real_escape_string($conn, $_details['program_id']) . "',                   
                program_name ='" . mysqli_real_escape_string($conn, $_details['program_name']) . "',                 
                course_type ='" . mysqli_real_escape_string($conn, $_details['course_type']) . "',                 
                course_id ='" . mysqli_real_escape_string($conn, $_details['course_id']) . "',                 
                country_id ='" . mysqli_real_escape_string($conn, $_details['country_id']) . "',                 
                city ='" . mysqli_real_escape_string($conn, $_details['city']) . "',                 
                institute_id ='" . mysqli_real_escape_string($conn, $_details['institute_id']) . "',                 
                deadline ='" . mysqli_real_escape_string($conn, $_details['deadline']) . "',                 
                currency_id ='" . mysqli_real_escape_string($conn, $_details['currency_id']) . "',                 
                commission ='" . mysqli_real_escape_string($conn, $_details['commission']) . "',                 
                tution_fee ='" . mysqli_real_escape_string($conn, $_details['tution_fee']) . "', 
                application_fee ='" . mysqli_real_escape_string($conn, $_details['application_fee']) . "', 
                intake ='" . mysqli_real_escape_string($conn, $_details['intake']) . "', 
                duration ='" . mysqli_real_escape_string($conn, $_details['duration']) . "', 
                ets ='" . mysqli_real_escape_string($conn, $_details['ets']) . "', 
                requirements ='" . mysqli_real_escape_string($conn, $_details['requirements']) . "', 
                english_requirements ='" . mysqli_real_escape_string($conn, $_details['english_requirements']) . "', 
                program_web_url ='" . mysqli_real_escape_string($conn, $_details['program_web_url']) . "' ,
                intake2 ='" . mysqli_real_escape_string($conn, $_details['intake2']) . "' ,
                deadline2 ='" . mysqli_real_escape_string($conn, $_details['deadline2']) . "' ,
                intake3 ='" . mysqli_real_escape_string($conn, $_details['intake3']) . "' ,
                deadline3 ='" . mysqli_real_escape_string($conn, $_details['deadline3']) . "' ,
                intake4 ='" . mysqli_real_escape_string($conn, $_details['intake4']) . "' ,
                deadline4 ='" . mysqli_real_escape_string($conn, $_details['deadline4']) . "'                
               ;";

        $res = mysqli_query($conn, $query);
        if ($res) {
            $program_id = mysqli_insert_id($conn);
            return $program_id;
        } else {
            return mysqli_error($conn);
        }
    }



    function deleteProgram($program_id)
    {
        $conn = $this->_conn;

        $query = "DELETE FROM `ggportal_tbl_program` WHERE `program_id` = $program_id;";

        $res = mysqli_query($conn, $query);
        // return $query;
        // die();


        if ($res) {
            return $program_id;
        } else {
            return mysqli_error($conn);
        }
    }



    function getProgramWhishlist($criteria)
    {
        $conn = $this->_conn;
        $cond = "";

        if (isset($criteria['program_id']) && strlen($criteria['program_id']) > 0) {
            $program_id = intval($criteria['program_id']);
            $cond .= " and pw.program_id  = $program_id ";
        }

        if (isset($criteria['user_id']) && strlen($criteria['user_id']) > 0) {
            $user_id = intval($criteria['user_id']);
            $cond .= " and pw.user_id  = $user_id ";
        }

        if (isset($criteria['user_type']) && strlen($criteria['user_type']) > 0) {
            $user_type = mysqli_real_escape_string($conn, $criteria['user_type']);
            $cond .= " and pw.user = '$user_type' ";
        }

        $query = "SELECT * from ggportal_tbl_program_wishlist pw
         where 1=1  $cond ";
        //    return $query;
        //        die();

        $result = mysqli_query($conn, $query);
        return db_to_array($result);
    }


    public function updateProgramWishlist($_details)
    {
        $conn = $this->_conn;
        $program_wishlist_id = intval($_details['program_wishlist_id']);

        //        print_R($driver_details);
        //        die();
        if ($program_wishlist_id == 0) {
            $query = "INSERT INTO ggportal_tbl_program_wishlist SET                    
                program_id = " . intval($_details['program_id']) . "                  
                ,user = '" . mysqli_real_escape_string($conn, $_details['user']) . "'                         
                ,user_id = " . intval($_details['user_id']) . "                                
               ;";
        } else {
            //update

            $query = "DELETE FROM `ggportal_tbl_program_wishlist`                       
            WHERE program_wishlist_id = $program_wishlist_id;  ";

        }
        // return $query;
        //        die();
        $res = mysqli_query($conn, $query);
        if ($res) {
            if ($program_wishlist_id == 0) {
                $program_wishlist_id = mysqli_insert_id($conn);
            }
            return $program_wishlist_id;
        } else {
            return mysqli_error($conn);
        }
    }
    //for bulk upload
    public function getProgramIDByName($program_name)
    {
        $conn = $this->_conn;
    
        // Escape program name to prevent SQL injection
        $program_name = mysqli_real_escape_string($conn, $program_name);
    
        $query = "SELECT program_id
                  FROM ggportal_tbl_program
                  WHERE program_name = '$program_name'";
                  
        $res = mysqli_query($conn, $query);
        
        if (!$res) {
            // Error handling - return false or handle the error appropriately
            return false;
        }
    
        $row = mysqli_fetch_assoc($res);
        if ($row) {
            return $row['program_id'];
        } else {
            // Program not found
            return false;
        }
    }

    public function updateProgram($program_id,$course_id,$institute_id, $_details)
    {
    $conn = $this->_conn;
    $course_name = mysqli_real_escape_string($conn, $_details['course_name']);

    // Check if the course name already exists
    $check_query = "SELECT course_id FROM ggportal_tbl_course WHERE course_name = '$course_name'";
    $check_result = mysqli_query($conn, $check_query);

    if(mysqli_num_rows($check_result) === 0) {
        // If course doesn't exist, insert it into ggportal_tbl_course
        $course_query = "INSERT INTO ggportal_tbl_course (course_name) VALUES ('$course_name')";
        $course_res = mysqli_query($conn, $course_query);
        if (!$course_res) {
            return mysqli_error($conn); // Return error if course insertion fails
        }
        // Retrieve the course_id of the newly inserted course
        $course_id = mysqli_insert_id($conn);
    } else {
        // If course already exists, retrieve its course_id
        $row = mysqli_fetch_assoc($check_result);
        $course_id = $row['course_id'];
    }

    // Now that we have or created the course, proceed to add/update the program
    $query = "UPDATE ggportal_tbl_program SET    
        program_id ='" . mysqli_real_escape_string($conn, $_details['program_id']) . "',     
        program_name = '" . mysqli_real_escape_string($conn, $_details['program_name']) . "',  
        course_type = '" . mysqli_real_escape_string($conn, $_details['course_type']) . "',                                
        course_id = $course_id,                  
        country_id = " . intval($_details['country_id']) . ",  
        city = '" . mysqli_real_escape_string($conn, $_details['city']) . "',                
        institute_id = " . intval($_details['institute_id']) . ", 
        deadline = '" . mysqli_real_escape_string($conn, $_details['deadline']) . "',                 
        currency_id = " . intval($_details['currency_id']) . ",                  
        commission = '" . mysqli_real_escape_string($conn, $_details['commission']) . "',                
        tution_fee = '" . mysqli_real_escape_string($conn, $_details['tution_fee']) . "',                
        application_fee = '" . mysqli_real_escape_string($conn, $_details['application_fee']) . "',                
        intake = '" . mysqli_real_escape_string($conn, $_details['intake']) . "',                
        duration = '" . mysqli_real_escape_string($conn, $_details['duration']) . "',                
        ets = '" . mysqli_real_escape_string($conn, $_details['ets']) . "',                
        requirements = '" . mysqli_real_escape_string($conn, $_details['requirements']) . "',
        english_requirements = '" . mysqli_real_escape_string($conn, $_details['english_requirements']) . "',
        program_web_url = '" . mysqli_real_escape_string($conn, $_details['program_web_url']) . "',
        intake2 = '" . mysqli_real_escape_string($conn, $_details['intake2']) . "',
        deadline2 = '" . mysqli_real_escape_string($conn, $_details['deadline2']) . "',
        intake3 = '" . mysqli_real_escape_string($conn, $_details['intake3']) . "',
        deadline3 = '" . mysqli_real_escape_string($conn, $_details['deadline3']) . "',
        intake4 = '" . mysqli_real_escape_string($conn, $_details['intake4']) . "',
        deadline4 = '" . mysqli_real_escape_string($conn, $_details['deadline4']) . "'
        WHERE program_id = $program_id";       

    $res = mysqli_query($conn, $query);
    if (!$res) {
        // Error handling - return false or handle the error appropriately
        echo "Error: " . mysqli_error($conn);
        return false;
    }

    return true;
}


    public function checkProgramExists($program_id) {
        $conn = $this->_conn;

        // Prepare the query
        $check_query = "SELECT program_id FROM ggportal_tbl_program WHERE program_id = ?";
        $stmt = mysqli_prepare($conn, $check_query);

        // Bind parameters
        mysqli_stmt_bind_param($stmt, "i", $program_id);

        // Execute the statement
        mysqli_stmt_execute($stmt);

        // Get the result
        $check_result = mysqli_stmt_get_result($stmt);

        // Check if program exists
        return mysqli_num_rows($check_result) > 0;
    }

    public function getProgramData() {
        $conn = $this->_conn;
        // Step 1: Query Data
        $query = "SELECT * FROM ggportal_tbl_program ORDER BY program_id";
        $result = mysqli_query($this->_conn, $query);

        // Check if there are any rows returned
        if (mysqli_num_rows($result) > 0) {
            // Initialize an array to store the data
            $data = array();

            // Fetch rows and store them in the data array
            while ($row = mysqli_fetch_assoc($result)) {
                $data[] = $row;
            }
            return $data;
        } else {
            // No data found, return null or perform any other action
            return null;
        }
    }

    
    public function getProgramRequirementsById($program_id)
    {
        $conn = $this->_conn;    
        
        mysqli_real_escape_string($conn, $program_id);
    
        $query = "SELECT requirements
                  FROM ggportal_tbl_program
                  WHERE program_id = '$program_id'";
                  
        $res = mysqli_query($conn, $query);
        
        if (!$res) {           
            return false;
        }
    
        $row = mysqli_fetch_assoc($res);
        if ($row) {
            return $row['requirements'];
        } else {
            // Program not found
            return false;
        }
    }
  

    public function getFilteredPrograms($country_to_apply_id = null, $course_type = null, $intake_name = null, $institute_name = null, $course_name_id = null, $duration = null, $min_tuition_fee= null,$max_tuition_fee= null,$min_application_fee= null,$max_application_fee = null) {                
        $conn = $this->_conn;   
      
        $query = "SELECT ggportal_tbl_program.*, ggportal_tbl_country.country_name, ggportal_tbl_institute.institute_name,logo_url FROM ggportal_tbl_program 
        LEFT join ggportal_tbl_country on ggportal_tbl_program.country_id = ggportal_tbl_country.country_id 
        LEFT join ggportal_tbl_institute on ggportal_tbl_program.institute_id = ggportal_tbl_institute.institute_id
        WHERE 1=1";

        $types = "";
        $values = [];

        if($country_to_apply_id ==="0" || $country_to_apply_id ==="null"){
            $country_to_apply_id=null;        
        }
        if($intake_name ==="0" || $intake_name ===""){
            $intake_name=null;
        }
        if($duration ==="0" || $duration ===""){
            $duration=null;
        }
        if($course_name_id ==="0" ||$course_name_id ===""){
            $course_name_id=null;
        }
        if($course_type ==="0" ||$course_type ===""){
            $course_type=null;
        }
        if($institute_name ==="0" ||$institute_name ==="" ||$institute_name ===false ){
            $institute_name=null;
        }
        
        $params = [
            "ggportal_tbl_program.country_id" => $country_to_apply_id,
            "course_type" => $course_type,
            "intake" => $intake_name,
            "course_id" => $course_name_id,
            "duration" => $duration,            
            "ggportal_tbl_program.institute_id" => $institute_name,            
        ];  
        
        foreach ($params as $key => $value) {
            if (!is_null($value)) {
                if (in_array($key, ['intake', 'course_id', 'duration'])) { // Assuming these are string fields
                    $query .= " AND $key LIKE ?";
                    $types .= "s";
                    $values[] = '%' . $value . '%'; // Adding wildcards for LIKE
                } else {
                    $query .= " AND $key = ?";
                    $types .= is_int($value) ? "i" : (is_double($value) ? "d" : "s");
                    $values[] = $value;
                }
            }
        }
        // filter max and min value
        if (!is_null($min_tuition_fee)) {
            $query .= " AND tution_fee >= ?";
            $types .= "d";
            $values[] = $min_tuition_fee;
        }
    
        if (!is_null($max_tuition_fee)) {
            $query .= " AND tution_fee <= ?";
            $types .= "d";
            $values[] = $max_tuition_fee;
        }
    
        if (!is_null($min_application_fee)) {
            $query .= " AND application_fee >= ?";
            $types .= "d";
            $values[] = $min_application_fee;
        }
    
        if (!is_null($max_application_fee)) {
            $query .= " AND application_fee <= ?";
            $types .= "d";
            $values[] = $max_application_fee;
        }

        // return  $values;
        error_log("Query: " . $query);
        error_log("Types: " . $types);
        error_log("Values: " . print_r($values, true));
    
        $stmt = $conn->prepare($query);
        if ($stmt === false) {
            return ['error' => 'Failed to prepare statement'];
        }
    
        if ($types) {
            if (!$stmt->bind_param($types, ...$values)) {
                return ['error' => 'Failed to bind parameters'];
            }
        }
    
        if (!$stmt->execute()) {
            return ['error' => 'Failed to execute statement'];
        }
    
        $result = $stmt->get_result();
    
        if ($result === false) {
            return ['error' => 'Failed to fetch programs'];
        }
    
        $programs = [];
        while ($row = $result->fetch_assoc()) {
            $programs[] = $row;
        }
    
        return $programs;
    }
    
    public function getAllPrograms(){       

        $conn = $this->_conn;   
      
        $query = "SELECT ggportal_tbl_program.*, ggportal_tbl_country.country_name, ggportal_tbl_institute.institute_name,logo_url FROM ggportal_tbl_program 
        LEFT join ggportal_tbl_country on ggportal_tbl_program.country_id = ggportal_tbl_country.country_id 
        LEFT join ggportal_tbl_institute on ggportal_tbl_program.institute_id = ggportal_tbl_institute.institute_id
        WHERE 1=1";

        $types = "";
        $values = [];
        
        
        error_log("Query: " . $query);
        error_log("Types: " . $types);
        error_log("Values: " . print_r($values, true));
    
        $stmt = $conn->prepare($query);
        if ($stmt === false) {
            return ['error' => 'Failed to prepare statement'];
        }
    
        if ($types) {
            if (!$stmt->bind_param($types, ...$values)) {
                return ['error' => 'Failed to bind parameters'];
            }
        }
    
        if (!$stmt->execute()) {
            return ['error' => 'Failed to execute statement'];
        }
    
        $result = $stmt->get_result();
    
        if ($result === false) {
            return ['error' => 'Failed to fetch programs'];
        }
    
        $programs = [];
        while ($row = $result->fetch_assoc()) {
            $programs[] = $row;
        }
    
        return $programs;
    }
    
        
    
}
